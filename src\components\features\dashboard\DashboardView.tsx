"use client";

import React, { useMemo } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, BarChart3 } from 'lucide-react';
import { Dashboard, ChartWidget as ChartWidgetType, CHART_CONFIG, UpdateDashboardRequest } from '@/types';
import ChartWidget from './ChartWidget';
import InlineEdit from '@/components/ui/inline-edit';

// Import CSS for react-grid-layout
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface DashboardViewProps {
  dashboard: Dashboard;
  widgets: ChartWidgetType[];
  onCreateChart: () => void;
  onDeleteWidget: (widgetId: string) => void;
  onUpdateWidget: (widgetId: string, updates: Partial<ChartWidgetType>) => void;
  onLayoutChange: (layout: any[]) => void;
  onUpdateDashboard: (dashboardId: string, updates: UpdateDashboardRequest) => Promise<void>;
  className?: string;
}

const DashboardView: React.FC<DashboardViewProps> = ({
  dashboard,
  widgets,
  onCreateChart,
  onDeleteWidget,
  onUpdateWidget,
  onLayoutChange,
  onUpdateDashboard,
  className = '',
}) => {
  // Generate layout from widgets
  const layouts = useMemo(() => {
    const layout = widgets.map((widget) => ({
      i: widget.id,
      x: widget.layout.x,
      y: widget.layout.y,
      w: widget.layout.w,
      h: widget.layout.h,
      minW: CHART_CONFIG.MIN_WIDGET_SIZE.w,
      minH: CHART_CONFIG.MIN_WIDGET_SIZE.h,
    }));

    return {
      lg: layout,
      md: layout,
      sm: layout.map(item => ({ ...item, w: Math.min(item.w, 6) })),
      xs: layout.map(item => ({ ...item, w: Math.min(item.w, 4) })),
      xxs: layout.map(item => ({ ...item, w: 2 })),
    };
  }, [widgets]);

  const canCreateChart = widgets.length < CHART_CONFIG.MAX_WIDGETS;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Dashboard Info */}
      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-6 w-6 text-primary" />
          <InlineEdit
            value={dashboard.name}
            onSave={(newName) => onUpdateDashboard(dashboard.id, { name: newName })}
            placeholder="Dashboard name..."
            displayClassName="text-3xl font-bold text-foreground"
            editClassName="text-3xl font-bold"
            maxLength={100}
            hideButtons={true}
          />
        </div>
      </div>

      {/* Chart Widgets Grid */}
      {widgets.length > 0 && (
        <div className="relative">
          <ResponsiveGridLayout
            className="layout"
            layouts={layouts}
            breakpoints={CHART_CONFIG.BREAKPOINTS}
            cols={CHART_CONFIG.GRID_COLS}
            rowHeight={CHART_CONFIG.ROW_HEIGHT}
            onLayoutChange={onLayoutChange}
            isDraggable={true}
            isResizable={true}
            margin={[16, 16]}
            containerPadding={[0, 0]}
          >
            {widgets.map((widget) => (
              <div key={widget.id} className="grid-item">
                <ChartWidget
                  widget={widget}
                  onDelete={onDeleteWidget}
                  onUpdate={onUpdateWidget}
                  className="h-full"
                />
              </div>
            ))}
          </ResponsiveGridLayout>
        </div>
      )}
    </div>
  );
};

export default DashboardView;
