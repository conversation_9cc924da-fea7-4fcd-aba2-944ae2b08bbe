"use client";
import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface PageHeaderInfo {
  title: string;
  subtitle?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface PageHeaderActions {
  onCreateChart?: () => void;
  chartCount?: number;
  maxCharts?: number;
}

interface PageHeaderContextType {
  pageInfo: PageHeaderInfo;
  actions: PageHeaderActions;
  setPageHeader: (info: PageHeaderInfo) => void;
  setPageActions: (actions: PageHeaderActions) => void;
  resetPageHeader: () => void;
}

const defaultPageInfo: PageHeaderInfo = {
  title: 'Agent Platform',
  subtitle: 'AI-Powered Data Analytics',
};

const defaultPageActions: PageHeaderActions = {
  onCreateChart: undefined,
  chartCount: 0,
  maxCharts: 12,
};

const PageHeaderContext = createContext<PageHeaderContextType | undefined>(undefined);

interface PageHeaderProviderProps {
  children: ReactNode;
}

export const PageHeaderProvider: React.FC<PageHeaderProviderProps> = ({ children }) => {
  const [pageInfo, setPageInfo] = useState<PageHeaderInfo>(defaultPageInfo);
  const [actions, setActions] = useState<PageHeaderActions>(defaultPageActions);

  const setPageHeader = useCallback((info: PageHeaderInfo) => {
    setPageInfo(info);
  }, []);

  const setPageActions = useCallback((newActions: PageHeaderActions) => {
    setActions(newActions);
  }, []);

  const resetPageHeader = useCallback(() => {
    setPageInfo(defaultPageInfo);
    setActions(defaultPageActions);
  }, []);

  return (
    <PageHeaderContext.Provider value={{ pageInfo, actions, setPageHeader, setPageActions, resetPageHeader }}>
      {children}
    </PageHeaderContext.Provider>
  );
};

export const usePageHeader = (): PageHeaderContextType => {
  const context = useContext(PageHeaderContext);
  if (context === undefined) {
    throw new Error('usePageHeader must be used within a PageHeaderProvider');
  }
  return context;
}; 