"use client";

import React, { useState, useMemo } from 'react';
import { usePageTitle } from '@/hooks/usePageTitle';
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { 
  Brain, 
  MessageSquare,
  Code2,
  BarChart3,
  Table,
  Play,
  Send,
  X,
  ChevronRight,
  ChevronLeft,
  Download,
  Copy,
  Maximize2,
  Database,
  Eye,
  Settings,
  Layers
} from 'lucide-react';

// Pipeline step types
interface PipelineStep {
  id: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  type: 'data_loading' | 'processing' | 'analysis' | 'visualization' | 'export';
  outputs?: {
    data?: any[];
    code?: string;
    visualization?: {
      type: 'chart' | 'graph' | 'table' | 'heatmap';
      config: any;
      data: any;
    };
    summary?: string;
  };
}

// Mock pipeline data
const MOCK_PIPELINE: PipelineStep[] = [
  {
    id: 'step-1',
    title: 'Data Loading',
    status: 'completed',
    type: 'data_loading',
    outputs: {
      data: [
        { id: 1, name: '<PERSON>', age: 32, department: 'Engineering', salary: 85000, performance: 4.2 },
        { id: 2, name: 'Jane <PERSON>', age: 28, department: 'Marketing', salary: 65000, performance: 4.8 },
        { id: 3, name: 'Mike Johnson', age: 35, department: 'Engineering', salary: 92000, performance: 4.1 },
        { id: 4, name: 'Sarah Wilson', age: 29, department: 'Design', salary: 70000, performance: 4.6 },
        { id: 5, name: 'David Brown', age: 41, department: 'Engineering', salary: 98000, performance: 3.9 },
      ],
      code: `import pandas as pd
import numpy as np

# Load employee data from connected database
df = pd.read_sql_query("""
    SELECT id, name, age, department, salary, performance_rating as performance
    FROM employees 
    WHERE active = true
""", connection)

print(f"Loaded {len(df)} employee records")
df.head()`,
      summary: "Successfully loaded 5 employee records from the connected database."
    }
  },
  {
    id: 'step-2',
    title: 'Data Analysis',
    status: 'completed',
    type: 'analysis',
    outputs: {
      code: `# Analyze salary distribution by department
dept_stats = df.groupby('department').agg({
    'salary': ['mean', 'median', 'std'],
    'performance': 'mean',
    'age': 'mean'
}).round(2)

print("Department Statistics:")
print(dept_stats)

# Identify high performers
high_performers = df[df['performance'] >= 4.5]
print(f"\\nHigh performers ({len(high_performers)} employees):")
print(high_performers[['name', 'department', 'performance']])`,
      summary: "Analyzed salary distributions and identified 2 high-performing employees."
    }
  },
  {
    id: 'step-3',
    title: 'Visualization',
    status: 'completed',
    type: 'visualization',
    outputs: {
      visualization: {
        type: 'chart',
        config: {
          title: 'Salary vs Performance by Department',
          xAxis: 'performance',
          yAxis: 'salary',
          groupBy: 'department'
        },
        data: [
          { x: 4.2, y: 85000, group: 'Engineering', name: 'John Doe' },
          { x: 4.8, y: 65000, group: 'Marketing', name: 'Jane Smith' },
          { x: 4.1, y: 92000, group: 'Engineering', name: 'Mike Johnson' },
          { x: 4.6, y: 70000, group: 'Design', name: 'Sarah Wilson' },
          { x: 3.9, y: 98000, group: 'Engineering', name: 'David Brown' },
        ]
      },
      code: `import matplotlib.pyplot as plt
import seaborn as sns

# Create scatter plot of salary vs performance
plt.figure(figsize=(10, 6))
sns.scatterplot(data=df, x='performance', y='salary', hue='department', s=100)
plt.title('Salary vs Performance by Department')
plt.xlabel('Performance Rating')
plt.ylabel('Salary ($)')
plt.legend(title='Department')
plt.tight_layout()
plt.show()`,
      summary: "Created scatter plot showing relationship between salary and performance across departments."
    }
  }
];

const AIWorkflowsPageContent = () => {
  // Set page title
  const pageConfig = useMemo(() => ({
    title: 'AI Workflows',
    icon: Brain
  }), []);

  usePageTitle(pageConfig);

  // State management
  const [chatMessage, setChatMessage] = useState('');
  const [isChatPanelOpen, setIsChatPanelOpen] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'data' | 'code' | 'visualizations'>('overview');
  const [selectedStep, setSelectedStep] = useState<string | null>(null);
  const [expandedCodeBlocks, setExpandedCodeBlocks] = useState<Set<string>>(new Set());
  const [pipeline] = useState<PipelineStep[]>(MOCK_PIPELINE);

  // Handle chat message send
  const handleSendMessage = () => {
    if (!chatMessage.trim()) return;
    console.log('Sending message:', chatMessage);
    setChatMessage('');
    // TODO: Implement chat functionality
  };

  // Toggle code block expansion
  const toggleCodeBlock = (stepId: string) => {
    const newExpanded = new Set(expandedCodeBlocks);
    if (newExpanded.has(stepId)) {
      newExpanded.delete(stepId);
    } else {
      newExpanded.add(stepId);
    }
    setExpandedCodeBlocks(newExpanded);
  };

  // Copy code to clipboard
  const copyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    // TODO: Show toast notification
  };

  // Render pipeline step status
  const renderStepStatus = (status: PipelineStep['status']) => {
    const statusConfig = {
      pending: { color: 'var(--sidebar-text-tertiary)', icon: '⏳', bg: 'rgba(156, 163, 175, 0.1)' },
      running: { color: '#f59e0b', icon: '🔄', bg: 'rgba(245, 158, 11, 0.1)' },
      completed: { color: '#10b981', icon: '✅', bg: 'rgba(16, 185, 129, 0.1)' },
      error: { color: '#ef4444', icon: '❌', bg: 'rgba(239, 68, 68, 0.1)' }
    };

    const config = statusConfig[status];
    return (
      <span 
        className="text-xs px-2 py-1 rounded-full inline-flex items-center gap-1"
        style={{ 
          color: config.color,
          backgroundColor: config.bg
        }}
      >
        <span>{config.icon}</span>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  // Render data table
  const renderDataTable = (data: any[]) => {
    if (!data || data.length === 0) return null;

    const columns = Object.keys(data[0]);
    
    return (
      <div 
        className="border rounded-lg overflow-hidden"
        style={{ borderColor: 'var(--sidebar-border)' }}
      >
        <div 
          className="overflow-x-auto"
          style={{ backgroundColor: 'var(--sidebar-bg)' }}
        >
          <table className="w-full">
            <thead>
              <tr style={{ borderBottom: '1px solid var(--sidebar-border)' }}>
                {columns.map((column) => (
                  <th 
                    key={column}
                    className="text-left px-4 py-3 text-sm font-medium"
                    style={{ 
                      color: 'var(--sidebar-text-primary)',
                      backgroundColor: 'var(--sidebar-surface-secondary)'
                    }}
                  >
                    {column.charAt(0).toUpperCase() + column.slice(1)}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.map((row, index) => (
                <tr 
                  key={index}
                  style={{ borderBottom: '1px solid var(--sidebar-border)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  {columns.map((column) => (
                    <td 
                      key={column}
                      className="px-4 py-3 text-sm"
                      style={{ color: 'var(--sidebar-text-secondary)' }}
                    >
                      {typeof row[column] === 'number' && column === 'salary' 
                        ? `$${row[column].toLocaleString()}`
                        : row[column]
                      }
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  // Render visualization placeholder
  const renderVisualization = (viz: any) => {
    return (
      <div 
        className="border rounded-lg p-6 text-center"
        style={{ 
          borderColor: 'var(--sidebar-border)',
          backgroundColor: 'var(--sidebar-bg)'
        }}
      >
        <BarChart3 
          className="h-16 w-16 mx-auto mb-4" 
          style={{ color: 'var(--sidebar-text-tertiary)' }}
        />
        <h4 
          className="text-lg font-medium mb-2"
          style={{ color: 'var(--sidebar-text-primary)' }}
        >
          {viz.config.title}
        </h4>
        <p 
          className="text-sm mb-4"
          style={{ color: 'var(--sidebar-text-secondary)' }}
        >
          Scatter plot showing {viz.data.length} data points
        </p>
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="border"
            style={{
              borderColor: 'var(--sidebar-border)',
              color: 'var(--sidebar-text-secondary)'
            }}
          >
            <Eye className="h-4 w-4 mr-2" />
            View Full
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="border"
            style={{
              borderColor: 'var(--sidebar-border)',
              color: 'var(--sidebar-text-secondary)'
            }}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div
      className="min-h-screen flex"
      style={{ backgroundColor: 'var(--sidebar-bg)' }}
      role="main"
      aria-label="AI Workflows"
    >
      {/* Main Content Area */}
      <div className={`flex-1 transition-all duration-300 ${isChatPanelOpen ? 'mr-96' : 'mr-0'}`}>
        <div className="h-full flex flex-col p-6">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 
                className="text-2xl font-semibold"
                style={{ color: 'var(--sidebar-text-primary)' }}
              >
                Employee Analysis Workflow
              </h1>
              <p 
                className="text-sm mt-1"
                style={{ color: 'var(--sidebar-text-secondary)' }}
              >
                Analyzing employee performance and salary data
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className="border"
                style={{
                  borderColor: 'var(--sidebar-border)',
                  color: 'var(--sidebar-text-secondary)'
                }}
              >
                <Download className="h-4 w-4 mr-2" />
                Export Results
              </Button>
              
              <Button
                onClick={() => setIsChatPanelOpen(!isChatPanelOpen)}
                variant="outline"
                size="sm"
                className="border"
                style={{
                  borderColor: 'var(--sidebar-border)',
                  color: 'var(--sidebar-text-secondary)'
                }}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                {isChatPanelOpen ? 'Hide' : 'Show'} Chat
              </Button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex items-center gap-1 mb-6">
            {[
              { id: 'overview', label: 'Overview', icon: Layers },
              { id: 'data', label: 'Data', icon: Table },
              { id: 'visualizations', label: 'Charts', icon: BarChart3 },
              { id: 'code', label: 'Code', icon: Code2 }
            ].map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <Button
                  key={tab.id}
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveTab(tab.id as any)}
                  className="h-10 px-4 border-0"
                  style={{
                    color: isActive ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',
                    backgroundColor: isActive ? 'var(--surface-selected)' : 'transparent'
                  }}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </Button>
              );
            })}
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-auto">
            
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Pipeline Steps */}
                <div className="grid gap-4">
                  {pipeline.map((step, index) => (
                    <Card
                      key={step.id}
                      className="border cursor-pointer transition-all duration-200"
                      style={{
                        backgroundColor: 'var(--sidebar-surface-secondary)',
                        borderColor: selectedStep === step.id ? '#3b82f6' : 'var(--sidebar-border)'
                      }}
                      onClick={() => setSelectedStep(selectedStep === step.id ? null : step.id)}
                      onMouseEnter={(e) => {
                        if (selectedStep !== step.id) {
                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (selectedStep !== step.id) {
                          e.currentTarget.style.backgroundColor = 'var(--sidebar-surface-secondary)';
                        }
                      }}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div 
                              className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                              style={{ 
                                backgroundColor: 'var(--surface-selected)',
                                color: 'var(--sidebar-text-primary)'
                              }}
                            >
                              {index + 1}
                            </div>
                            <div>
                              <CardTitle 
                                className="text-base"
                                style={{ color: 'var(--sidebar-text-primary)' }}
                              >
                                {step.title}
                              </CardTitle>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            {renderStepStatus(step.status)}
                            <ChevronRight 
                              className={`h-4 w-4 transition-transform duration-200 ${
                                selectedStep === step.id ? 'rotate-90' : ''
                              }`}
                              style={{ color: 'var(--sidebar-text-tertiary)' }}
                            />
                          </div>
                        </div>
                      </CardHeader>
                      
                      {/* Expanded Step Content */}
                      {selectedStep === step.id && step.outputs && (
                        <CardContent className="pt-0 space-y-4">
                          {step.outputs.summary && (
                            <p 
                              className="text-sm"
                              style={{ color: 'var(--sidebar-text-secondary)' }}
                            >
                              {step.outputs.summary}
                            </p>
                          )}
                          
                          {/* Step Data */}
                          {step.outputs.data && (
                            <div>
                              <h4 
                                className="text-sm font-medium mb-2 flex items-center gap-2"
                                style={{ color: 'var(--sidebar-text-primary)' }}
                              >
                                <Table className="h-4 w-4" />
                                Data Output ({step.outputs.data.length} rows)
                              </h4>
                              {renderDataTable(step.outputs.data)}
                            </div>
                          )}
                          
                          {/* Step Visualization */}
                          {step.outputs.visualization && (
                            <div>
                              <h4 
                                className="text-sm font-medium mb-2 flex items-center gap-2"
                                style={{ color: 'var(--sidebar-text-primary)' }}
                              >
                                <BarChart3 className="h-4 w-4" />
                                Visualization
                              </h4>
                              {renderVisualization(step.outputs.visualization)}
                            </div>
                          )}
                          
                          {/* Step Code */}
                          {step.outputs.code && (
                            <div>
                              <div className="flex items-center justify-between mb-2">
                                <h4 
                                  className="text-sm font-medium flex items-center gap-2"
                                  style={{ color: 'var(--sidebar-text-primary)' }}
                                >
                                  <Code2 className="h-4 w-4" />
                                  Generated Code
                                </h4>
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      copyCode(step.outputs!.code!);
                                    }}
                                    className="h-7 px-2 border-0"
                                    style={{ color: 'var(--sidebar-text-tertiary)' }}
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleCodeBlock(step.id);
                                    }}
                                    className="h-7 px-2 border-0"
                                    style={{ color: 'var(--sidebar-text-tertiary)' }}
                                  >
                                    <Maximize2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                              <div 
                                className={`border rounded-lg p-3 font-mono text-xs overflow-x-auto transition-all duration-200 ${
                                  expandedCodeBlocks.has(step.id) ? 'max-h-96' : 'max-h-32'
                                }`}
                                style={{ 
                                  backgroundColor: 'var(--sidebar-bg)',
                                  borderColor: 'var(--sidebar-border)',
                                  color: 'var(--sidebar-text-secondary)'
                                }}
                              >
                                <pre>{step.outputs.code}</pre>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Data Tab */}
            {activeTab === 'data' && (
              <div className="space-y-6">
                {pipeline
                  .filter(step => step.outputs?.data)
                  .map((step) => (
                    <div key={step.id}>
                      <h3 
                        className="text-lg font-medium mb-4 flex items-center gap-2"
                        style={{ color: 'var(--sidebar-text-primary)' }}
                      >
                        <Database className="h-5 w-5" />
                        {step.title} - Data Output
                      </h3>
                      {renderDataTable(step.outputs!.data!)}
                    </div>
                  ))}
              </div>
            )}

            {/* Visualizations Tab */}
            {activeTab === 'visualizations' && (
              <div className="space-y-6">
                {pipeline
                  .filter(step => step.outputs?.visualization)
                  .map((step) => (
                    <div key={step.id}>
                      <h3 
                        className="text-lg font-medium mb-4 flex items-center gap-2"
                        style={{ color: 'var(--sidebar-text-primary)' }}
                      >
                        <BarChart3 className="h-5 w-5" />
                        {step.title} - Visualization
                      </h3>
                      {renderVisualization(step.outputs!.visualization!)}
                    </div>
                  ))}
              </div>
            )}

            {/* Code Tab */}
            {activeTab === 'code' && (
              <div className="space-y-6">
                {pipeline
                  .filter(step => step.outputs?.code)
                  .map((step) => (
                    <div key={step.id}>
                      <div className="flex items-center justify-between mb-4">
                        <h3 
                          className="text-lg font-medium flex items-center gap-2"
                          style={{ color: 'var(--sidebar-text-primary)' }}
                        >
                          <Code2 className="h-5 w-5" />
                          {step.title} - Generated Code
                        </h3>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyCode(step.outputs!.code!)}
                          className="border"
                          style={{
                            borderColor: 'var(--sidebar-border)',
                            color: 'var(--sidebar-text-secondary)'
                          }}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Code
                        </Button>
                      </div>
                      <div 
                        className="border rounded-lg p-4 font-mono text-sm overflow-x-auto"
                        style={{ 
                          backgroundColor: 'var(--sidebar-bg)',
                          borderColor: 'var(--sidebar-border)',
                          color: 'var(--sidebar-text-secondary)'
                        }}
                      >
                        <pre>{step.outputs!.code}</pre>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Chat Panel */}
      {isChatPanelOpen && (
        <div 
          className="fixed right-0 top-0 w-96 h-full border-l flex flex-col"
          style={{
            backgroundColor: 'var(--sidebar-surface-secondary)',
            borderColor: 'var(--sidebar-border)'
          }}
        >
          {/* Chat Header */}
          <div 
            className="p-4 border-b flex items-center justify-between"
            style={{ borderColor: 'var(--sidebar-border)' }}
          >
            <h3 
              className="font-medium"
              style={{ color: 'var(--sidebar-text-primary)' }}
            >
              AI Assistant
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsChatPanelOpen(false)}
              className="h-8 w-8 p-0 border-0"
              style={{ color: 'var(--sidebar-text-tertiary)' }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 p-4 overflow-auto">
            <div 
              className="text-sm text-center"
              style={{ color: 'var(--sidebar-text-tertiary)' }}
            >
              Start a conversation to modify your analysis, ask questions, or generate new insights.
            </div>
          </div>

          {/* Chat Input */}
          <div 
            className="p-4 border-t"
            style={{ borderColor: 'var(--sidebar-border)' }}
          >
            <div className="flex gap-2">
              <Input
                placeholder="Ask about your data or request changes..."
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                className="flex-1 text-sm border"
                style={{
                  backgroundColor: 'var(--sidebar-bg)',
                  borderColor: 'var(--sidebar-border)',
                  color: 'var(--sidebar-text-primary)'
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && chatMessage.trim()) {
                    handleSendMessage();
                  }
                }}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!chatMessage.trim()}
                size="sm"
                className="px-3 border-0"
                style={{
                  backgroundColor: chatMessage.trim() ? 'var(--surface-selected)' : 'transparent',
                  color: chatMessage.trim() ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-tertiary)'
                }}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIWorkflowsPageContent;