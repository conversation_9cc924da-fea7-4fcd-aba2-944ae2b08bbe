"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  Dashboard as DashboardType,
  ChartWidget as ChartWidgetType,
  CHART_CONFIG,
  DashboardNavigationState,
  CreateDashboardRequest,
  UpdateDashboardRequest
} from '@/types';
import { useApi } from '@/providers/ApiContext';
import { usePageTitle } from '@/hooks/usePageTitle';
import { usePageHeader } from '@/providers/PageHeaderContext';
import { LayoutDashboard } from 'lucide-react';
import DashboardList from './DashboardList';
import DashboardView from './DashboardView';
import DashboardHeader from './DashboardHeader';

const Dashboard: React.FC = () => {
  // Navigation state
  const [navigationState, setNavigationState] = useState<DashboardNavigationState>({
    currentView: 'list',
    selectedDashboard: null,
    breadcrumbs: [{ label: 'Dashboards' }],
  });

  // Dashboard data
  const [dashboards, setDashboards] = useState<DashboardType[]>([]);
  const [dashboardStats, setDashboardStats] = useState<Record<string, number>>({});
  const [isLoadingDashboards, setIsLoadingDashboards] = useState(false);

  // Current dashboard widgets
  const [widgets, setWidgets] = useState<ChartWidgetType[]>([]);
  const [nextWidgetId, setNextWidgetId] = useState(1);

  // Dashboard creation state
  const [isCreatingDashboard, setIsCreatingDashboard] = useState(false);

  // API and utilities
  const {
    listDashboards,
    createDashboard,
    updateDashboard,
    deleteDashboard
  } = useApi();
  const { setPageActions } = usePageHeader();
  // const { toast } = useToast();

  // Set page title dynamically based on navigation state - memoized to prevent re-renders
  const pageConfig = useMemo(() => ({
    title: navigationState.currentView === 'dashboard' && navigationState.selectedDashboard 
      ? `Dashboard > ${navigationState.selectedDashboard.name}`
      : 'Dashboard',
    icon: LayoutDashboard
  }), [navigationState.currentView, navigationState.selectedDashboard]);

  usePageTitle(pageConfig);

  // Load dashboards on component mount
  useEffect(() => {
    loadDashboards();
  }, []);

  const loadDashboards = useCallback(async () => {
    setIsLoadingDashboards(true);
    try {
      const response = await listDashboards();
      if (response.success) {
        setDashboards(response.data);
        // Mock dashboard stats - in real app, this would come from API
        const stats: Record<string, number> = {};
        response.data.forEach((dashboard: DashboardType) => {
          stats[dashboard.id] = Math.floor(Math.random() * 8); // Random chart count for demo
        });
        setDashboardStats(stats);
      }
    } catch (error) {
      console.error('Failed to load dashboards:', error);
      // toast({
      //   title: "Error",
      //   description: "Failed to load dashboards. Please try again.",
      //   variant: "destructive",
      // });
    } finally {
      setIsLoadingDashboards(false);
    }
  }, [listDashboards]);

  // Navigation handlers
  const handleSelectDashboard = useCallback((dashboard: DashboardType) => {
    setNavigationState({
      currentView: 'dashboard',
      selectedDashboard: dashboard,
      breadcrumbs: [
        { label: 'Dashboards', onClick: () => handleNavigateBack() },
        { label: dashboard.name },
      ],
    });
    // Load dashboard widgets - for now, start with empty
    setWidgets([]);
    setNextWidgetId(1);
  }, []);

  const handleNavigateBack = useCallback(() => {
    setNavigationState({
      currentView: 'list',
      selectedDashboard: null,
      breadcrumbs: [{ label: 'Dashboards' }],
    });
    setWidgets([]);
  }, []);

  // Dashboard CRUD handlers
  const handleCreateDashboard = useCallback(async () => {
    setIsCreatingDashboard(true);
    try {
      // Create dashboard with default name
      const defaultName = `Dashboard ${dashboards.length + 1}`;
      const response = await createDashboard({
        name: defaultName,
        description: '',
      });

      if (response.success) {
        const newDashboard = response.data;
        setDashboards(prev => [...prev, newDashboard]);
        setDashboardStats(prev => ({ ...prev, [newDashboard.id]: 0 }));

        // Immediately navigate to the new dashboard
        handleSelectDashboard(newDashboard);
      }
    } catch (error) {
      console.error('Failed to create dashboard:', error);
    } finally {
      setIsCreatingDashboard(false);
    }
  }, [dashboards.length, createDashboard, handleSelectDashboard]);

  const handleUpdateDashboard = useCallback(async (dashboardId: string, updates: UpdateDashboardRequest) => {
    try {
      const response = await updateDashboard(dashboardId, updates);
      if (response.success) {
        setDashboards(prev => prev.map(d =>
          d.id === dashboardId ? response.data : d
        ));

        // Update the selected dashboard if it's the one being edited
        if (navigationState.selectedDashboard?.id === dashboardId) {
          setNavigationState(prev => ({
            ...prev,
            selectedDashboard: response.data,
            breadcrumbs: [
              { label: 'Dashboards', onClick: () => handleNavigateBack() },
              { label: response.data.name },
            ],
          }));
        }
      }
    } catch (error) {
      console.error('Failed to update dashboard:', error);
    }
  }, [updateDashboard, navigationState.selectedDashboard, handleNavigateBack]);

  const handleDeleteDashboard = useCallback(async (dashboardId: string) => {
    if (!confirm('Are you sure you want to delete this dashboard? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await deleteDashboard(dashboardId);
      if (response.success) {
        setDashboards(prev => prev.filter(d => d.id !== dashboardId));
        setDashboardStats(prev => {
          const newStats = { ...prev };
          delete newStats[dashboardId];
          return newStats;
        });

        // If we're currently viewing the deleted dashboard, navigate back
        if (navigationState.selectedDashboard?.id === dashboardId) {
          handleNavigateBack();
        }

        // toast({
        //   title: "Success",
        //   description: "Dashboard deleted successfully.",
        // });
      }
    } catch (error) {
      console.error('Failed to delete dashboard:', error);
      // toast({
      //   title: "Error",
      //   description: "Failed to delete dashboard. Please try again.",
      //   variant: "destructive",
      // });
    }
  }, [deleteDashboard, navigationState.selectedDashboard, handleNavigateBack]);

  // Chart widget management
  const handleCreateChart = useCallback(() => {
    if (widgets.length >= CHART_CONFIG.MAX_WIDGETS) {
      // toast({
      //   title: "Limit Reached",
      //   description: `Maximum of ${CHART_CONFIG.MAX_WIDGETS} charts allowed per dashboard.`,
      //   variant: "destructive",
      // });
      return;
    }

    // Find next available position
    let x = 0;
    let y = 0;

    // Simple positioning logic - place widgets in rows
    const widgetsPerRow = Math.floor(12 / CHART_CONFIG.DEFAULT_WIDGET_SIZE.w);
    const currentRow = Math.floor(widgets.length / widgetsPerRow);
    const currentCol = widgets.length % widgetsPerRow;

    x = currentCol * CHART_CONFIG.DEFAULT_WIDGET_SIZE.w;
    y = currentRow * CHART_CONFIG.DEFAULT_WIDGET_SIZE.h;

    const newWidget: ChartWidgetType = {
      id: `widget-${nextWidgetId}`,
      title: `Chart ${nextWidgetId}`,
      chartData: null,
      isLoading: false,
      error: null,
      dashboard_id: navigationState.selectedDashboard?.id,
      layout: {
        x,
        y,
        w: CHART_CONFIG.DEFAULT_WIDGET_SIZE.w,
        h: CHART_CONFIG.DEFAULT_WIDGET_SIZE.h,
      },
    };

    setWidgets(prev => [...prev, newWidget]);
    setNextWidgetId(prev => prev + 1);
  }, [widgets, nextWidgetId, navigationState.selectedDashboard]);

  const handleDeleteWidget = useCallback((widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  }, []);

  const handleUpdateWidget = useCallback((widgetId: string, updates: Partial<ChartWidgetType>) => {
    setWidgets(prev => prev.map(w =>
      w.id === widgetId ? { ...w, ...updates } : w
    ));
  }, []);

  const handleLayoutChange = useCallback((layout: any[]) => {
    // Update widget positions based on grid layout changes
    setWidgets(prev => prev.map(widget => {
      const layoutItem = layout.find(item => item.i === widget.id);
      if (layoutItem) {
        return {
          ...widget,
          layout: {
            ...widget.layout,
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          },
        };
      }
      return widget;
    }));
  }, []);

  // Update page actions when navigation state or widgets change
  useEffect(() => {
    const isDashboardView = navigationState.currentView === 'dashboard' && navigationState.selectedDashboard;
    setPageActions({
      onCreateChart: isDashboardView ? handleCreateChart : undefined,
      chartCount: widgets.length,
      maxCharts: CHART_CONFIG.MAX_WIDGETS,
    });
  }, [navigationState.currentView, navigationState.selectedDashboard, widgets.length, setPageActions]);

  return (
    <div className="min-h-screen bg-sidebar-bg">
      <div className="container mx-auto p-6 space-y-6">
        {/* Dashboard Header */}
      

        {/* Main Content */}
        {navigationState.currentView === 'list' ? (
          <DashboardList
            dashboards={dashboards}
            dashboardStats={dashboardStats}
            onSelectDashboard={handleSelectDashboard}
            onCreateDashboard={handleCreateDashboard}
            onDeleteDashboard={handleDeleteDashboard}
          />
        ) : navigationState.selectedDashboard ? (
          <DashboardView
            dashboard={navigationState.selectedDashboard}
            widgets={widgets}
            onCreateChart={handleCreateChart}
            onDeleteWidget={handleDeleteWidget}
            onUpdateWidget={handleUpdateWidget}
            onLayoutChange={handleLayoutChange}
            onUpdateDashboard={handleUpdateDashboard}
          />
        ) : null}
      </div>
    </div>
  );
};

export default Dashboard;