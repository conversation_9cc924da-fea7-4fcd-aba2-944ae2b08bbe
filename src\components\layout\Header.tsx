"use client";
import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu, LayoutDashboard, ChevronRight, Plus } from 'lucide-react';
import { useAuth } from '@/providers/AuthContext';
import { useTheme } from "@/providers/theme-provider";
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { usePageHeader } from '@/providers/PageHeaderContext';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

const Header = () => {
  const { isAuthenticated } = useAuth();
  const { theme, setTheme } = useTheme();
  const { pageInfo, actions } = usePageHeader();
  const pathname = usePathname();
  
  // Check if we're on a dashboard page and if title contains breadcrumb
  const isDashboardPage = pathname === '/dashboard';
  const titleParts = pageInfo.title.includes(' > ') ? pageInfo.title.split(' > ') : [pageInfo.title];
  const showBreadcrumbs = isDashboardPage && titleParts.length > 1;
  
  // Get chart-related state from context actions
  const { onCreateChart, chartCount = 0, maxCharts = 12 } = actions;
  const canCreateChart = chartCount < maxCharts;
  
  // Show Create Chart Button on dashboard pages when the callback is provided
  const showCreateChartButton = isDashboardPage && onCreateChart;

  return (
    <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-sidebar-border bg-sidebar-bg px-6 py-3 h-16">
      <div className="flex items-center gap-4 text-sidebar-text-primary">
        {isAuthenticated && (
          <div className="lg:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
            </Sheet>
          </div>
        )}
        
        {/* Dashboard Breadcrumb Navigation or Page Title */}
        {showBreadcrumbs ? (
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink 
                  href="/dashboard"
                  className="flex items-center space-x-2 cursor-pointer hover:text-primary text-lg font-semibold"
                >
                  <LayoutDashboard className="h-5 w-5" />
                  <span>{titleParts[0]}</span>
                </BreadcrumbLink>
              </BreadcrumbItem>
              
              <BreadcrumbSeparator>
                <ChevronRight className="h-4 w-4" />
              </BreadcrumbSeparator>
              
              <BreadcrumbItem>
                <BreadcrumbPage className="font-semibold text-lg">
                  {titleParts[1]}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        ) : (
          <h1 className="text-lg font-semibold leading-tight tracking-[-0.015em] text-sidebar-text-primary">
            {pageInfo.title}
          </h1>
        )}
      </div>

      {/* Right side actions */}
      <div className="flex items-center gap-2">
        {showCreateChartButton && (
          <Button
            onClick={onCreateChart}
            disabled={!canCreateChart}
            className="rounded-lg"
            title={!canCreateChart ? `Maximum of ${maxCharts} charts allowed` : undefined}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Chart
          </Button>
        )}
      </div>
    </header>
  );
};

export default Header;